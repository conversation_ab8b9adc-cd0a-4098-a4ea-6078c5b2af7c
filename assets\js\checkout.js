// Wait for Stripe to be available
const waitForStripe = () => {
  return new Promise((resolve) => {
    if (typeof Stripe !== 'undefined') {
      resolve();
    } else {
      const checkStripe = setInterval(() => {
        if (typeof Stripe !== 'undefined') {
          clearInterval(checkStripe);
          resolve();
        }
      }, 100);
    }
  });
};

let stripeInstance;

// Initialize after Stripe is available
waitForStripe().then(() => {
  //stripeInstance = Stripe("***********************************************************************************************************");
  stripeInstance = Stripe("pk_test_51ReWsQQ9NwEfvhkVRh3kDlpmfn1ZR7JTY8RV2SVJn6HpO0MTpLXq8wVAV6qptZZ84rjiHeYcL3s2Mq3SphDdTPcv001qongYjw");

  // Create a Checkout Session with 1 second delay
  setTimeout(() => {
    initialize();
  }, 1000);
});

// Create a Checkout Session
async function initialize() {
  const fetchClientSecret = async () => {
    const response = await fetch("/create-checkout-session", {
      method: "POST",
    });
    const { clientSecret, publishableKey } = await response.json();
    stripeInstance = Stripe(publishableKey);  // Update stripe instance with the server-provided key
    return clientSecret;
  };

  const checkout = await stripeInstance.initEmbeddedCheckout({
    fetchClientSecret,
  });

  // Mount Checkout
  checkout.mount('#checkout');
}