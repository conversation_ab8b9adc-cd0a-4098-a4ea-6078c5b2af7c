:root, [data-bs-theme=light] {
  --bs-primary: #3D6486;
  --bs-primary-rgb: 61,100,134;
  --bs-primary-text-emphasis: #182836;
  --bs-primary-bg-subtle: #D8E0E7;
  --bs-primary-border-subtle: #B1C1CF;
  --bs-secondary: #706d9f;
  --bs-secondary-rgb: 112,109,159;
  --bs-secondary-text-emphasis: #2D2C40;
  --bs-secondary-bg-subtle: #E2E2EC;
  --bs-secondary-border-subtle: #C6C5D9;
  --bs-light: #e6f4f1;
  --bs-light-rgb: 230,244,241;
  --bs-light-text-emphasis: #5C6260;
  --bs-light-bg-subtle: #FAFDFC;
  --bs-light-border-subtle: #F5FBF9;
  --bs-dark: #3b4856;
  --bs-dark-rgb: 59,72,86;
  --bs-dark-text-emphasis: #181D22;
  --bs-dark-bg-subtle: #D8DADD;
  --bs-dark-border-subtle: #B1B6BB;
  --bs-body-font-family: 'Open Sans', sans-serif;
}

.btn-primary {
  --bs-btn-color: #fff;
  --bs-btn-bg: #3D6486;
  --bs-btn-border-color: #3D6486;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #345572;
  --bs-btn-hover-border-color: #31506B;
  --bs-btn-focus-shadow-rgb: 226,232,237;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #31506B;
  --bs-btn-active-border-color: #2E4B65;
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #3D6486;
  --bs-btn-disabled-border-color: #3D6486;
}

.btn-outline-primary {
  --bs-btn-color: #3D6486;
  --bs-btn-border-color: #3D6486;
  --bs-btn-focus-shadow-rgb: 61,100,134;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #3D6486;
  --bs-btn-hover-border-color: #3D6486;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #3D6486;
  --bs-btn-active-border-color: #3D6486;
  --bs-btn-disabled-color: #3D6486;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #3D6486;
}

.btn-secondary {
  --bs-btn-color: #fff;
  --bs-btn-bg: #706d9f;
  --bs-btn-border-color: #706d9f;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #5F5D87;
  --bs-btn-hover-border-color: #5A577F;
  --bs-btn-focus-shadow-rgb: 234,233,241;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #5A577F;
  --bs-btn-active-border-color: #545277;
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #706d9f;
  --bs-btn-disabled-border-color: #706d9f;
}

.btn-outline-secondary {
  --bs-btn-color: #706d9f;
  --bs-btn-border-color: #706d9f;
  --bs-btn-focus-shadow-rgb: 112,109,159;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #706d9f;
  --bs-btn-hover-border-color: #706d9f;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #706d9f;
  --bs-btn-active-border-color: #706d9f;
  --bs-btn-disabled-color: #706d9f;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #706d9f;
}

.btn-light {
  --bs-btn-color: #000000;
  --bs-btn-bg: #e6f4f1;
  --bs-btn-border-color: #e6f4f1;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #C4CFCD;
  --bs-btn-hover-border-color: #B8C3C1;
  --bs-btn-focus-shadow-rgb: 35,37,36;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #B8C3C1;
  --bs-btn-active-border-color: #ADB7B5;
  --bs-btn-disabled-color: #000000;
  --bs-btn-disabled-bg: #e6f4f1;
  --bs-btn-disabled-border-color: #e6f4f1;
}

.btn-outline-light {
  --bs-btn-color: #e6f4f1;
  --bs-btn-border-color: #e6f4f1;
  --bs-btn-focus-shadow-rgb: 230,244,241;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #e6f4f1;
  --bs-btn-hover-border-color: #e6f4f1;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #e6f4f1;
  --bs-btn-active-border-color: #e6f4f1;
  --bs-btn-disabled-color: #e6f4f1;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #e6f4f1;
}

.btn-dark {
  --bs-btn-color: #fff;
  --bs-btn-bg: #3b4856;
  --bs-btn-border-color: #3b4856;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #323D49;
  --bs-btn-hover-border-color: #2F3A45;
  --bs-btn-focus-shadow-rgb: 226,228,230;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #2F3A45;
  --bs-btn-active-border-color: #2C3641;
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #3b4856;
  --bs-btn-disabled-border-color: #3b4856;
}

.btn-outline-dark {
  --bs-btn-color: #3b4856;
  --bs-btn-border-color: #3b4856;
  --bs-btn-focus-shadow-rgb: 59,72,86;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #3b4856;
  --bs-btn-hover-border-color: #3b4856;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #3b4856;
  --bs-btn-active-border-color: #3b4856;
  --bs-btn-disabled-color: #3b4856;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #3b4856;
}

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}

.my-2 {
  margin-top: .5rem !important;
  margin-bottom: .5rem !important;
}

.my-3 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mt-2 {
  margin-top: .5rem !important;
}

.mt-3 {
  margin-top: 1rem !important;
}

.mt-5 {
  margin-top: 3rem !important;
}

.me-3 {
  margin-right: 1rem !important;
}

.me-auto {
  margin-right: auto !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-2 {
  margin-bottom: .5rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

.mb-5 {
  margin-bottom: 3rem !important;
}

.ms-2 {
  margin-left: .5rem !important;
}

.ms-3 {
  margin-left: 1rem !important;
}

.p-0 {
  padding: 0 !important;
}

.p-3 {
  padding: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.px-4 {
  padding-right: 1.5rem !important;
  padding-left: 1.5rem !important;
}

.py-2 {
  padding-top: .5rem !important;
  padding-bottom: .5rem !important;
}

.py-3 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

.py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.py-5 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pt-3 {
  padding-top: 1rem !important;
}

.pt-4 {
  padding-top: 1.5rem !important;
}

.pt-5 {
  padding-top: 3rem !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pb-3 {
  padding-bottom: 1rem !important;
}

.pb-4 {
  padding-bottom: 1.5rem !important;
}

.ps-0 {
  padding-left: 0 !important;
}

@media (min-width: 992px) {
  .p-lg-5 {
    padding: 3rem !important;
  }
}

@media (min-width: 992px) {
  .py-lg-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
}

@media (min-width: 992px) {
  .py-lg-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
}

@media (min-width: 1200px) {
  .m-xl-5 {
    margin: 3rem !important;
  }
}

@media (min-width: 1200px) {
  .p-xl-4 {
    padding: 1.5rem !important;
  }
}

@media (min-width: 1200px) {
  .p-xl-5 {
    padding: 3rem !important;
  }
}

@media (min-width: 1200px) {
  .py-xl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
}

